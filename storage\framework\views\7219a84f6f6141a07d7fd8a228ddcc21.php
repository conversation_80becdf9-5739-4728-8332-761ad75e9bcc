<?php $__env->startSection('json-ld'); ?>
    <script type="application/ld+json">
                    {
                        "@context": "https://schema.org",
                        "@type": "WebPage",
                        "name": "<?php echo e(get_setting('site_name')); ?>",
                        "description": "<?php echo e($metaDescription); ?>",
                        "publisher": {
                            "@type": "Organization",
                            "name": "<?php echo e(get_setting('schema_organization_name')); ?>"
                        },
                        "url": "<?php echo e(url()->current()); ?>",
                        "datePublished": "<?php echo e(now()->toIso8601String()); ?>",
                        "dateModified": "<?php echo e(now()->toIso8601String()); ?>"
                    }
                </script>

    <?php if(!empty($breadcrumbs)): ?>
        <?php echo $__env->make('pincodes.json-ld.breadcrumbs', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['metaDescription' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($metaDescription)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
    <div class="container max-w-6xl mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
            <!-- Main content column -->
            <div class="lg:col-span-8">
                <!-- Pincode Wise Post Offices -->
                <div class="bg-white dark:bg-bg-dark shadow-md rounded-lg p-6">
                    <?php if($errors->has('pincode')): ?>
                        <div class="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-500 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
                            <?php echo e($errors->first('pincode')); ?>

                        </div>
                    <?php elseif($pincodes->isNotEmpty()): ?>
                        <h2 class="text-3xl font-bold text-primary-light dark:text-primary-dark mb-4"><?php echo e($pageTitle); ?></h2>

                        <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark mb-3">
                            <?php if($po_count > 1): ?>
                                    A single pincode is not limited to just one post office; a single postal code can be
                                    assigned to multiple post offices within the same region.
                                </p>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">In the case of pincode
                                    <?php echo e($pincodes->first()->pincode); ?>, it is assigned to <?php echo e($po_count); ?> different
                                    post offices,
                                    which include
                                    <?php $__currentLoopData = $pincodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $po): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($index == $po_count - 1): ?>
                                            and <?php echo e(ucfirst($po->name)); ?>.
                                        <?php elseif($index == $po_count - 2): ?>
                                            <?php echo e(ucfirst($po->name)); ?>

                                        <?php else: ?>
                                            <?php echo e(ucfirst($po->name)); ?>,
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark">Pincode <?php echo e($pincodes->first()->pincode); ?> is assigned to only one post
                                    office:
                                    <?php echo e(ucfirst($pincodes->first()->name)); ?>.
                                </p>
                            <?php endif; ?>
                        </p>

                        <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">These post offices, associated with pincode
                            <?php echo e($pincodes->first()->pincode); ?>,
                            are located in
                            the
                            <?php echo e($district); ?> district of <?php echo e($state); ?> state. To learn more about each
                            individual post office,
                            click on the respective name in the list below.
                        </p>

                        <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">Additionally, you can explore user
                            reviews for each postal code to verify their accuracy and gain insights from others'
                            experiences.
                        </p>

                        <div class="mb-4">
                            <div class="bg-bg-light dark:bg-gray-800 p-4 rounded-lg">
                                <h2 class="text-xl font-semibold mb-3 text-text-primary-light dark:text-text-primary-dark">Post Offices (<?php echo e($po_count); ?>)</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <?php $__currentLoopData = $pincodes->chunk(ceil($pincodes->count() / 2)); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div>
                                            <ul class="space-y-2">
                                                <?php $__currentLoopData = $chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $pincode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="bg-white dark:bg-bg-dark p-3 rounded-lg shadow-sm">
                                                        <a href="<?php echo e(url('/pincodes/' . rawurlencode($pincode->state) . '/' . rawurlencode($pincode->district) . '/' . rawurlencode($pincode->name))); ?>"
                                                            class="text-primary-light dark:text-primary-dark hover:underline">
                                                            <?php echo e($loop->parent->iteration == 1 ? $loop->iteration : $loop->iteration + ceil($pincodes->count() / 2)); ?>.
                                                            <?php echo e(ucfirst($pincode->name)); ?> -
                                                            <?php echo e($pincode->pincode); ?>

                                                        </a>
                                                    </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <?php if(count($villageList) > 0): ?>
                            <div class="mb-4">
                                <h2 class="text-2xl font-bold mb-3 text-text-primary-light dark:text-text-primary-dark">Villages under Pincode <?php echo e($pincode->pincode); ?></h2>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">
                                    Total <?php echo e(count($villageList)); ?> villages come under the Pincode
                                    <?php echo e($pincode->pincode); ?>. Villagers of the following villages use this pincode for
                                    postal communication.
                                </p>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                    <?php $__currentLoopData = $villageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $village): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="bg-white dark:bg-bg-dark p-4 rounded-lg shadow-sm">
                                            <h5 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark"><?php echo e($village->village_name_en); ?></h5>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mb-4">
                            <h2 class="text-2xl font-bold mb-3 text-text-primary-light dark:text-text-primary-dark">Location Map</h2>
                            <?php if(isset($pincode->latitude) && isset($pincode->longitude)): ?>
                                <div class="aspect-w-16 aspect-h-9">
                                    <iframe
                                        src="https://maps.google.com/maps?q=<?php echo e($pincode->latitude); ?>,<?php echo e($pincode->longitude); ?>&hl=es;z=14&amp;output=embed"
                                        allowfullscreen="" loading="lazy" class="w-full h-full rounded-lg"></iframe>
                                </div>
                            <?php else: ?>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark">Location data is not available.</p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if($relatedPincodes->isNotEmpty()): ?>
                    <div class="bg-white dark:bg-bg-dark shadow-md rounded-lg p-6 mt-4">
                        <h3 class="text-2xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Other Post Offices in <?php echo e(ucfirst($pincode->district)); ?>

                            District</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php $__currentLoopData = $relatedPincodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $pincode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-white dark:bg-bg-dark p-4 rounded-lg shadow-sm border border-border-light dark:border-border-dark">
                                    <h5 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                                        <span class="bg-primary-light dark:bg-primary-dark text-white px-2 py-1 rounded-full mr-2"><?php echo e($index + 1); ?></span>
                                        Pincode: <?php echo e($pincode->pincode); ?>

                                    </h5>
                                    <ul class="space-y-1 text-text-secondary-light dark:text-text-secondary-dark">
                                        <li><strong>Name:</strong> <a
                                                href="<?php echo e(url('/pincodes/' . rawurlencode($pincode->state) . '/' . rawurlencode($pincode->district) . '/' . rawurlencode($pincode->name))); ?>"
                                                class="text-primary-light dark:text-primary-dark hover:underline"><?php echo e(ucfirst($pincode->name)); ?></a>
                                        </li>
                                        <li><strong>District:</strong> <a
                                                href="<?php echo e(url('/pincodes/' . rawurlencode($pincode->state) . '/' . rawurlencode($pincode->district))); ?>"
                                                class="text-primary-light dark:text-primary-dark hover:underline"><?php echo e(ucfirst($pincode->district)); ?></a>
                                        </li>
                                        <li><strong>Division:</strong> <?php echo e($pincode->division); ?></li>
                                        <li><strong>Circle:</strong> <?php echo e($pincode->circle); ?></li>
                                    </ul>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="mt-4">
                    <?php echo $__env->make('pincodes.partials.share', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>

            <!-- Sidebar column -->
            <div class="lg:col-span-4 px-4">
                <div class="sticky top-20">
                    <?php echo $__env->make('pincodes.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            // Smooth scroll to anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (event) {
                    event.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        window.scrollTo({
                            top: target.offsetTop - 100,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Initialize tooltips
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltipTriggerList.forEach(tooltipTriggerEl => {
                new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/5-pincode-wise-post-offices.blade.php ENDPATH**/ ?>